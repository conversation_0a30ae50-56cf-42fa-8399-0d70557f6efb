[{"timestamp": "2025-07-05T17:15:12.277098", "output_id": "output_20250705_171512_320d922a", "input_id": "input_20250705_171503_e087daad", "prompt_id": "prompt_20250705_171503_f0e61f12", "raw_response": {"analysis_date": "2025-01-01", "analysis_period": "2025-01-01 to 2025-01-01", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "signal": "neutral", "comment": "RSI is close to the middle line indicating no strong overbought or oversold conditions."}, "MACD": {"signal": "neutral", "comment": "MACD line is close to the signal line, suggesting no clear trend direction."}, "Moving_Averages": {"50_day_MA": 105.0, "200_day_MA": 95.0, "signal": "neutral", "comment": "The stock is currently between the 50-day and 200-day moving averages, indicating a lack of strong trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T17:15:12.277098", "processing_time": 8.556277, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-01", "analysis_period": "2025-01-01 to 2025-01-01", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "signal": "neutral", "comment": "RSI is close to the middle line indicating no strong overbought or oversold conditions."}, "MACD": {"signal": "neutral", "comment": "MACD line is close to the signal line, suggesting no clear trend direction."}, "Moving_Averages": {"50_day_MA": 105.0, "200_day_MA": 95.0, "signal": "neutral", "comment": "The stock is currently between the 50-day and 200-day moving averages, indicating a lack of strong trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T17:15:12.277098", "processing_time": 8.556277, "llm_used": true}, "processing_time": 8.556277, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 8.556277}}]