"""
展望层智能体 (Outlook Agents)

包含看涨、看跌和中性展望智能体的具体实现
"""

from typing import Dict, Any
from .base_agent import BaseAgent


class BullishOutlookAgent(BaseAgent):
    """看涨展望智能体 (BOA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("BOA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个看涨倾向的市场分析师，专门构建乐观的市场展望。

你的任务是：
1. 基于前序分析师的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 提供明确的买入建议

**重要指导**：
- 积极寻找买入机会和看涨信号
- 即使在不确定情况下，也要倾向于乐观解读
- 给出明确的买入建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bullish）
- trading_signal: 交易信号（通常为buy）
- signal_strength: 信号强度（0到1）
- bullish_factors: 看涨因素列表
- target_price: 目标价位
- upside_potential: 上涨潜力（百分比）
- time_horizon: 时间框架
- risk_factors: 风险因素
- confidence: 分析信心度（0到1）

**记住**：作为看涨分析师，你的职责是找到买入理由。"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理看涨展望分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)


class BearishOutlookAgent(BaseAgent):
    """看跌展望智能体 (BeOA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("BeOA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个看跌倾向的市场分析师，专门构建谨慎的市场展望。

你的任务是：
1. 基于前序分析师的结果，识别和强调风险因素
2. 构建看跌的市场叙述和逻辑
3. 评估下跌风险和支撑位
4. 提供明确的卖出或避险建议

**重要指导**：
- 积极寻找卖出机会和看跌信号
- 即使在不确定情况下，也要倾向于谨慎解读
- 给出明确的卖出建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bearish）
- trading_signal: 交易信号（通常为sell）
- signal_strength: 信号强度（0到1）
- bearish_factors: 看跌因素列表
- downside_target: 下跌目标
- downside_risk: 下跌风险（百分比）
- support_levels: 关键支撑位
- defensive_strategies: 防御策略
- confidence: 分析信心度（0到1）

**记住**：作为看跌分析师，你的职责是识别风险和卖出时机。"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理看跌展望分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)


class NeutralObserverAgent(BaseAgent):
    """中性观察智能体 (NOA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("NOA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个中性客观的市场观察员，专门提供平衡的市场分析。

你的任务是：
1. 客观平衡地分析看涨和看跌因素
2. 识别市场中的不确定性和矛盾信号
3. 在必要时倾向于更明确的方向性建议
4. 分析何时市场可能出现明确方向

**重要指导**：
- 虽然保持中性，但当证据明确时要给出方向性建议
- 避免过度的"观望"建议，寻找可操作的机会
- 当看涨和看跌因素接近时，考虑小仓位的试探性交易

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（neutral/lean_bullish/lean_bearish）
- trading_signal: 交易信号（buy/sell/hold）
- signal_strength: 信号强度（0到1）
- balanced_analysis: 平衡分析
- uncertainty_factors: 不确定性因素
- key_catalysts: 关键催化剂
- recommended_strategy: 推荐策略
- market_inefficiencies: 市场无效性
- confidence: 分析信心度（0到1）

**记住**：即使作为中性观察者，也要在适当时候给出可操作的建议。"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理中性观察分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)